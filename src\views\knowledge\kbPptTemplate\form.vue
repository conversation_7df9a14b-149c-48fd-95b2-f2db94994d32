<template>
	<el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable>
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="24" class="mb20">
					<el-form-item label="模板名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入模板名称" />
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="模板内容" prop="content">
						<div class="">
							<pre>{{ form.content }}</pre>
						</div>
						<upload-file v-model:imageUrl="form.content"></upload-file>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="模板图片" prop="cover">
						<upload-img v-model="form.cover"></upload-img>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="模板标记" prop="tag">
						<el-select v-model="form.tag" placeholder="请选择模板标记">
							<el-option label="请选择">0</el-option>
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="KbPptTemplateDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj } from '/@/api/knowledge/kbPptTemplate';
import { rule } from '/@/utils/validate';
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典

// 提交表单数据
const form = reactive({
	id: '',
	content: '',
	name: '',
	cover: '',
	tag: '',
});

// 定义校验规则
const dataRules = ref({
	content: [{ required: true, message: '模板内容不能为空', trigger: 'blur' }],
	name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
	cover: [{ required: true, message: '模板图片不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 获取kbPptTemplate信息
	if (id) {
		form.id = id;
		getkbPptTemplateData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		loading.value = true;
		form.id ? await putObj(form) : await addObj(form);
		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据
const getkbPptTemplateData = (id: string) => {
	// 获取数据
	loading.value = true;
	getObj(id)
		.then((res: any) => {
			Object.assign(form, res.data);
		})
		.finally(() => {
			loading.value = false;
		});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
