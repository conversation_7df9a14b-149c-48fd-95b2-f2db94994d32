import request from "/@/utils/request"

export function fetchList(query?: Object) {
  return request({
    url: '/knowledge/kbPptTemplate/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj?: Object) {
  return request({
    url: '/knowledge/kbPptTemplate',
    method: 'post',
    data: obj
  })
}

export function getObj(id?: string) {
  return request({
    url: '/knowledge/kbPptTemplate/' + id,
    method: 'get'
  })
}

export function delObjs(ids?: Object) {
  return request({
    url: '/knowledge/kbPptTemplate',
    method: 'delete',
    data: ids
  })
}

export function putObj(obj?: Object) {
  return request({
    url: '/knowledge/kbPptTemplate',
    method: 'put',
    data: obj
  })
}

