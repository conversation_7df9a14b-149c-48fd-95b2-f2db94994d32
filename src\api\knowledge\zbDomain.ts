import request from '/@/utils/request';

export const domainTree = (params?: Object) => {
	return request({
		url: '/knowledge/zbDomain/tree',
		method: 'get',
		params,
	});
};

export function fetchList(query?: Object) {
	return request({
		url: '/knowledge/zbDomain/page',
		method: 'get',
		params: query,
	});
}

export function addObj(obj?: Object) {
	return request({
		url: '/knowledge/zbDomain',
		method: 'post',
		data: obj,
	});
}

export function getObj(id?: string) {
	return request({
		url: '/knowledge/zbDomain/' + id,
		method: 'get',
	});
}

export function delObjs(id: String) {
	return request({
		url: '/knowledge/zbDomain/' + id,
		method: 'delete',
	});
}

export function putObj(obj?: Object) {
	return request({
		url: '/knowledge/zbDomain',
		method: 'put',
		data: obj,
	});
}

export function checkDuplicateDomain(bizname: string, name: string) {
	return request({
		url: '/knowledge/zbDomain/checkDuplicate',
		method: 'get',
		params: { bizname, name },
	});
}

export function getTest() {
	return request({
		url: '/knowledge/test/test',
		method: 'get',
	});
}
